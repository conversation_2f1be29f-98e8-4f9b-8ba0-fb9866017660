@extends('admin.dashboard')

@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h4 class="mb-0">Edit Course</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('admin.courses.index') }}">Courses</a></li>
                                <li class="breadcrumb-item active">Edit Course</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h4 class="card-title">Course Information</h4>
                            <p class="card-title-desc">Update the course details below.</p>
                            
                            @if ($errors->any())
                                <div class="alert alert-danger">
                                    <ul class="mb-0">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            @if (session('success'))
                                <div class="alert alert-success">
                                    {{ session('success') }}
                                </div>
                            @endif

                            <form action="{{ route('admin.courses.update', $id) }}" method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                                @csrf
                                @method('PUT')
                                
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label" for="title">Course Title</label>
                                            <input type="text" class="form-control" id="title" name="title" 
                                                   placeholder="Enter course title" value="{{ old('title', 'Sample Course Title') }}" required>
                                            <div class="invalid-feedback">
                                                Please provide a valid course title.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="status">Status</label>
                                            <select class="form-select" id="status" name="status" required>
                                                <option value="">Select Status</option>
                                                <option value="draft" {{ old('status', 'draft') == 'draft' ? 'selected' : '' }}>Draft</option>
                                                <option value="published" {{ old('status') == 'published' ? 'selected' : '' }}>Published</option>
                                                <option value="archived" {{ old('status') == 'archived' ? 'selected' : '' }}>Archived</option>
                                            </select>
                                            <div class="invalid-feedback">
                                                Please select a status.
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label class="form-label" for="short_description">Short Description</label>
                                            <textarea class="form-control" id="short_description" name="short_description" rows="3" 
                                                      placeholder="Brief description of the course">{{ old('short_description', 'This is a sample course description that provides an overview of what students will learn.') }}</textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="category_id">Category</label>
                                            <select class="form-select" id="category_id" name="category_id" required>
                                                <option value="">Select Category</option>
                                                <option value="1" {{ old('category_id', '1') == '1' ? 'selected' : '' }}>Programming</option>
                                                <option value="2" {{ old('category_id') == '2' ? 'selected' : '' }}>Design</option>
                                                <option value="3" {{ old('category_id') == '3' ? 'selected' : '' }}>Business</option>
                                            </select>
                                            <div class="invalid-feedback">
                                                Please select a category.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="instructor_id">Instructor</label>
                                            <select class="form-select" id="instructor_id" name="instructor_id" required>
                                                <option value="">Select Instructor</option>
                                                <option value="1" {{ old('instructor_id', '1') == '1' ? 'selected' : '' }}>John Doe</option>
                                                <option value="2" {{ old('instructor_id') == '2' ? 'selected' : '' }}>Jane Smith</option>
                                            </select>
                                            <div class="invalid-feedback">
                                                Please select an instructor.
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="duration">Duration (hours)</label>
                                            <input type="number" class="form-control" id="duration" name="duration" 
                                                   placeholder="0" value="{{ old('duration', '10') }}" min="0" step="0.5">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="difficulty_level">Difficulty Level</label>
                                            <select class="form-select" id="difficulty_level" name="difficulty_level">
                                                <option value="">Select Level</option>
                                                <option value="beginner" {{ old('difficulty_level', 'beginner') == 'beginner' ? 'selected' : '' }}>Beginner</option>
                                                <option value="intermediate" {{ old('difficulty_level') == 'intermediate' ? 'selected' : '' }}>Intermediate</option>
                                                <option value="advanced" {{ old('difficulty_level') == 'advanced' ? 'selected' : '' }}>Advanced</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="language">Language</label>
                                            <select class="form-select" id="language" name="language">
                                                <option value="">Select Language</option>
                                                <option value="english" {{ old('language', 'english') == 'english' ? 'selected' : '' }}>English</option>
                                                <option value="spanish" {{ old('language') == 'spanish' ? 'selected' : '' }}>Spanish</option>
                                                <option value="french" {{ old('language') == 'french' ? 'selected' : '' }}>French</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="price">Price ($)</label>
                                            <input type="number" class="form-control" id="price" name="price" 
                                                   placeholder="0.00" value="{{ old('price', '99.99') }}" min="0" step="0.01">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label" for="discount_price">Discount Price ($)</label>
                                            <input type="number" class="form-control" id="discount_price" name="discount_price" 
                                                   placeholder="0.00" value="{{ old('discount_price', '79.99') }}" min="0" step="0.01">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <div class="form-check mt-4">
                                                <input type="checkbox" class="form-check-input" id="is_free" name="is_free" value="1" 
                                                       {{ old('is_free', false) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_free">
                                                    Free Course
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="is_featured" name="is_featured" value="1" 
                                                       {{ old('is_featured', true) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_featured">
                                                    Featured Course
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="thumbnail">Course Thumbnail</label>
                                            <input type="file" class="form-control" id="thumbnail" name="thumbnail" accept="image/*">
                                            <div class="form-text">Upload a new thumbnail (optional)</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label" for="preview_video">Preview Video URL</label>
                                            <input type="url" class="form-control" id="preview_video" name="preview_video" 
                                                   placeholder="https://youtube.com/watch?v=..." value="{{ old('preview_video', 'https://youtube.com/watch?v=sample') }}">
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end">
                                    <a href="{{ route('admin.courses.index') }}" class="btn btn-secondary me-2">
                                        <i class="uil-arrow-left me-1"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="uil-check me-1"></i> Update Course
                                    </button>
                                    <button type="button" class="btn btn-danger ms-2" onclick="deleteCourse()">
                                        <i class="uil-trash me-1"></i> Delete
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <script>document.write(new Date().getFullYear())</script> © Lernovate.
                </div>
                <div class="col-sm-6">
                    <div class="text-sm-end d-none d-sm-block">
                        Crafted with <i class="mdi mdi-heart text-danger"></i> by Lernovate Team
                    </div>
                </div>
            </div>
        </div>
    </footer>
</div>

{{-- Delete Confirmation Modal --}}
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this course? This action cannot be undone and will remove all associated data.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ route('admin.courses.destroy', $id) }}" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Auto-generate slug from course title
    document.getElementById('title').addEventListener('input', function() {
        const title = this.value;
        const slug = title.toLowerCase()
            .replace(/[^a-z0-9 -]/g, '') // Remove invalid chars
            .replace(/\s+/g, '-') // Replace spaces with -
            .replace(/-+/g, '-') // Replace multiple - with single -
            .trim('-'); // Trim - from start and end
        
        // If there's a slug field, update it
        const slugField = document.getElementById('slug');
        if (slugField) {
            slugField.value = slug;
        }
    });

    // Toggle pricing based on free course checkbox
    document.getElementById('is_free').addEventListener('change', function() {
        const priceInput = document.getElementById('price');
        const discountPriceInput = document.getElementById('discount_price');
        
        if (this.checked) {
            priceInput.disabled = true;
            discountPriceInput.disabled = true;
            priceInput.value = '0';
            discountPriceInput.value = '0';
        } else {
            priceInput.disabled = false;
            discountPriceInput.disabled = false;
        }
    });

    // Delete course function
    function deleteCourse() {
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Trigger free course checkbox change to set initial state
        document.getElementById('is_free').dispatchEvent(new Event('change'));
    });
</script>
@endsection
